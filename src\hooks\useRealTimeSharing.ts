'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { RealTimeUser, RealTimeMessage } from '@/types/boss';
import { backendService } from '@/lib/supabase';

interface PeerConnection {
  id: string;
  connection: RTCPeerConnection;
  dataChannel?: RTCDataChannel;
  user?: RealTimeUser;
}

interface SignalingData {
  type: 'offer' | 'answer' | 'ice-candidate' | 'user-list';
  from: string;
  to?: string;
  data: unknown;
  timestamp: number;
}

interface RoomData {
  users: RealTimeUser[];
  signals: SignalingData[];
  lastUpdated: number;
  hasPassword: boolean;
  password?: string;
  host: string;
  isActive: boolean;
}

// ICE servers for WebRTC
const ICE_SERVERS = [
  { urls: 'stun:stun.l.google.com:19302' },
  { urls: 'stun:stun1.l.google.com:19302' },
];

const USER_COLORS = [
  '#3B82F6', '#EF4444', '#10B981', '#F59E0B',
  '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16'
];

export function useRealTimeSharing() {
  const [isConnected, setIsConnected] = useState(false);
  const [isHost, setIsHost] = useState(false);
  const [roomId, setRoomId] = useState<string | null>(null);
  const [currentUser, setCurrentUser] = useState<RealTimeUser | null>(null);
  const [connectedUsers, setConnectedUsers] = useState<RealTimeUser[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const [error, setError] = useState<string | null>(null);

  const peersRef = useRef<Map<string, PeerConnection>>(new Map());
  const messageHandlersRef = useRef<((message: RealTimeMessage) => void)[]>([]);
  const isInitializedRef = useRef(false);
  const signalingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastSignalCheckRef = useRef<number>(0);

  // Generate a simple room ID
  const generateRoomId = useCallback(() => {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }, []);

  // Generate user ID and color
  const generateUser = useCallback((name: string): RealTimeUser => {
    const id = Math.random().toString(36).substring(2, 15);
    const color = USER_COLORS[Math.floor(Math.random() * USER_COLORS.length)];
    return {
      id,
      name,
      color,
      lastSeen: new Date(),
    };
  }, []);

  // Backend-powered cross-device sharing
  const lastBackendCheckRef = useRef<number>(0);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Get room data from backend with local cache
  const getRoomData = useCallback(async (roomId: string): Promise<RoomData | null> => {
    try {
      // Try local cache first for speed
      const localData = localStorage.getItem(`room-cache-${roomId}`);
      if (localData) {
        const parsed = JSON.parse(localData);
        const cacheAge = Date.now() - parsed.cachedAt;
        if (cacheAge < 5000) { // Use cache if less than 5 seconds old
          return parsed.data;
        }
      }

      // Fetch from backend
      const roomData = await backendService.getRoomData(roomId);

      // Cache the result
      if (roomData) {
        localStorage.setItem(`room-cache-${roomId}`, JSON.stringify({
          data: roomData,
          cachedAt: Date.now()
        }));
      }

      return roomData;
    } catch (error) {
      console.error('Failed to get room data:', error);
      // Fallback to local cache even if expired
      const localData = localStorage.getItem(`room-cache-${roomId}`);
      if (localData) {
        const parsed = JSON.parse(localData);
        return parsed.data;
      }
      return null;
    }
  }, []);

  const setRoomData = useCallback(async (roomId: string, data: RoomData) => {
    try {
      // Update backend
      await backendService.updateRoom(roomId, {
        ...data,
        lastActivity: Date.now()
      });

      // Update local cache
      localStorage.setItem(`room-cache-${roomId}`, JSON.stringify({
        data: {
          ...data,
          lastActivity: Date.now()
        },
        cachedAt: Date.now()
      }));

      console.log('Room data saved for room:', roomId, 'with', data.users.length, 'users');
    } catch (error) {
      console.error('Failed to save room data:', error);
      // Still update local cache as fallback
      localStorage.setItem(`room-cache-${roomId}`, JSON.stringify({
        data: {
          ...data,
          lastActivity: Date.now()
        },
        cachedAt: Date.now()
      }));
    }
  }, []);

  const addSignal = useCallback(async (roomId: string, signal: SignalingData) => {
    const roomData = (await getRoomData(roomId)) || {
      id: roomId,
      users: [],
      signals: [],
      lastActivity: Date.now(),
      hasPassword: false,
      host: '',
      isActive: true,
      createdAt: new Date().toISOString()
    };
    roomData.signals = roomData.signals || [];
    roomData.signals.push(signal);
    // Keep only recent signals (last 5 minutes)
    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
    roomData.signals = roomData.signals.filter(s => s.timestamp > fiveMinutesAgo);
    await setRoomData(roomId, roomData);
  }, [getRoomData, setRoomData]);

  const updateUserList = useCallback(async (roomId: string, users: RealTimeUser[]) => {
    const roomData = (await getRoomData(roomId)) || {
      id: roomId,
      users: [],
      signals: [],
      lastActivity: Date.now(),
      hasPassword: false,
      host: '',
      isActive: true,
      createdAt: new Date().toISOString()
    };
    roomData.users = users;
    await setRoomData(roomId, roomData);
  }, [getRoomData, setRoomData]);

  // Get list of available rooms from backend
  const getAvailableRooms = useCallback(async () => {
    try {
      const roomList = await backendService.getRoomList();
      const now = Date.now();
      const oneHourAgo = now - 60 * 60 * 1000;

      return Object.values(roomList)
        .filter((room: any) => {
          return room.lastActivity > oneHourAgo && room.isActive !== false;
        })
        .map((room: any) => ({
          id: room.id,
          userCount: room.userCount || 0,
          lastActivity: room.lastActivity,
          host: room.host || 'Unknown',
          hasPassword: room.hasPassword || false
        }));
    } catch (error) {
      console.error('Failed to get available rooms:', error);
      return [];
    }
  }, []);

  // Send message to all connected peers using backend
  const broadcastMessage = useCallback(async (message: RealTimeMessage) => {
    console.log('Broadcasting message:', message.type, 'from:', message.userName);

    if (!roomId) return;

    try {
      const messageData = {
        id: `${roomId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        roomId,
        userId: message.userId,
        userName: message.userName,
        type: message.type,
        data: message.data,
        timestamp: Date.now(),
        createdAt: new Date().toISOString()
      };

      await backendService.sendMessage(messageData);
      console.log('Message sent to backend for room:', roomId);
    } catch (error) {
      console.error('Failed to broadcast message:', error);
      // Store locally as fallback
      const localMessages = JSON.parse(localStorage.getItem('fallback-messages') || '{}');
      const messageKey = `${roomId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      localMessages[messageKey] = {
        ...message,
        roomId,
        timestamp: Date.now()
      };
      localStorage.setItem('fallback-messages', JSON.stringify(localMessages));
    }
  }, [roomId]);

  // WebRTC functions removed for simplicity - using localStorage messaging instead

  // Process signaling and messages from backend
  const processSignaling = useCallback(async () => {
    if (!roomId || !currentUser) return;

    try {
      const roomData = await getRoomData(roomId);
      if (!roomData) return;

      // Update connected users list
      const activeUsers = roomData.users.filter(u => {
        const timeSinceLastSeen = Date.now() - new Date(u.lastSeen).getTime();
        return timeSinceLastSeen < 60000; // 60 seconds timeout
      });

      setConnectedUsers(activeUsers);

      // Check for new messages from backend
      try {
        const newMessages = await backendService.getMessages(roomId, lastSignalCheckRef.current);

        newMessages.forEach(message => {
          // Only process messages from other users
          if (message.userId !== currentUser.id) {
            console.log('Processing message from backend:', message.type, 'from:', message.userName);
            messageHandlersRef.current.forEach(handler => handler({
              type: message.type as any,
              userId: message.userId,
              userName: message.userName,
              timestamp: new Date(message.createdAt),
              data: message.data
            }));
          }
        });
      } catch (messageError) {
        console.error('Failed to get messages from backend:', messageError);
      }

      lastSignalCheckRef.current = Date.now();

    } catch (error) {
      console.error('Error in processSignaling:', error);
    }
  }, [roomId, currentUser, getRoomData]);

  // Add message handler
  const addMessageHandler = useCallback((handler: (message: RealTimeMessage) => void) => {
    messageHandlersRef.current.push(handler);
    return () => {
      messageHandlersRef.current = messageHandlersRef.current.filter(h => h !== handler);
    };
  }, []);

  // Simple password hashing for room protection
  const hashPassword = useCallback((password: string): string => {
    // Simple hash function for client-side password protection
    let hash = 0;
    for (let i = 0; i < password.length; i++) {
      const char = password.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }, []);

  // Create a new room (host)
  const createRoom = useCallback(async (userName: string, password?: string): Promise<string> => {
    try {
      console.log('Creating room for user:', userName, 'with password:', !!password);
      setConnectionStatus('connecting');
      setError(null);

      const newRoomId = generateRoomId();
      const user = generateUser(userName);

      console.log('Generated room ID:', newRoomId, 'User:', user);

      // Initialize room data with better metadata
      const roomData = {
        id: newRoomId,
        users: [user],
        signals: [],
        lastActivity: Date.now(),
        hasPassword: !!password,
        password: password ? hashPassword(password) : undefined,
        host: user.id,
        isActive: true,
        createdAt: new Date().toISOString()
      };

      // Create room in backend
      await backendService.createRoom(roomData);

      // Also cache locally
      await setRoomData(newRoomId, roomData);

      // Also store a simplified room info for easier discovery
      localStorage.setItem(`room-info-${newRoomId}`, JSON.stringify({
        id: newRoomId,
        host: user.name,
        created: Date.now(),
        userCount: 1,
        hasPassword: !!password
      }));

      setRoomId(newRoomId);
      setCurrentUser(user);
      setIsHost(true);
      setConnectedUsers([user]);
      setIsConnected(true);
      setConnectionStatus('connected');

      // Store room info in localStorage for recovery
      localStorage.setItem('realtime-room', JSON.stringify({
        roomId: newRoomId,
        user,
        isHost: true,
      }));

      // Start signaling interval
      if (signalingIntervalRef.current) {
        clearInterval(signalingIntervalRef.current);
      }
      signalingIntervalRef.current = setInterval(() => {
        processSignaling();
      }, 1000);

      console.log('Room created successfully:', newRoomId);
      return newRoomId;
    } catch (error) {
      console.error('Failed to create room:', error);
      setError(error instanceof Error ? error.message : 'Failed to create room');
      setConnectionStatus('disconnected');
      throw error;
    }
  }, [generateRoomId, generateUser, setRoomData, processSignaling, hashPassword]);

  // Join an existing room
  const joinRoom = useCallback(async (roomId: string, userName: string, password?: string): Promise<void> => {
    try {
      console.log('Joining room:', roomId, 'as user:', userName, 'with password:', !!password);
      setConnectionStatus('connecting');
      setError(null);

      // Check if room exists with detailed logging
      console.log('Searching for room:', roomId);
      const roomData = await getRoomData(roomId);
      console.log('Room data found:', roomData);

      if (!roomData) {
        // Try to provide helpful error information
        const availableRooms = getAvailableRooms();
        console.log('Available rooms:', availableRooms);

        if (availableRooms.length === 0) {
          setError('Room not found. No active rooms available. Make sure the host has created a room first.');
          setConnectionStatus('disconnected');
          return;
        } else {
          const roomList = availableRooms.map(r => `${r.id} (${r.userCount} users)`).join(', ');
          setError(`Room "${roomId}" not found. Available rooms: ${roomList}`);
          setConnectionStatus('disconnected');
          return;
        }
      }

      // Check if room is active
      if (!roomData.isActive) {
        setError('This room has been closed by the host.');
        setConnectionStatus('disconnected');
        return;
      }

      // Check password if required
      if (roomData.hasPassword) {
        if (!password) {
          setError('This room requires a password.');
          setConnectionStatus('disconnected');
          return;
        }

        const hashedPassword = hashPassword(password);
        if (hashedPassword !== roomData.password) {
          setError('Incorrect password.');
          setConnectionStatus('disconnected');
          return;
        }
      }

      const user = generateUser(userName);
      console.log('Generated user for joining:', user);

      // Add user to room
      const updatedUsers = [...roomData.users, user];
      await updateUserList(roomId, updatedUsers);

      setRoomId(roomId);
      setCurrentUser(user);
      setIsHost(false);
      setConnectedUsers(updatedUsers);
      setIsConnected(true);
      setConnectionStatus('connected');

      // Store room info in localStorage for recovery
      localStorage.setItem('realtime-room', JSON.stringify({
        roomId,
        user,
        isHost: false,
      }));

      // Start signaling interval
      if (signalingIntervalRef.current) {
        clearInterval(signalingIntervalRef.current);
      }
      signalingIntervalRef.current = setInterval(() => {
        processSignaling();
      }, 1000);

      // Send user join message
      const joinMessage: RealTimeMessage = {
        type: 'user_join',
        userId: user.id,
        userName: user.name,
        timestamp: new Date(),
        data: {},
      };
      await broadcastMessage(joinMessage);

      console.log('Successfully joined room:', roomId);
    } catch (error) {
      console.error('Failed to join room:', error);
      setError(error instanceof Error ? error.message : 'Failed to join room');
      setConnectionStatus('disconnected');
    }
  }, [generateUser, getRoomData, updateUserList, getAvailableRooms, processSignaling, hashPassword, broadcastMessage]);

  // Leave the current room
  const leaveRoom = useCallback(() => {
    // Send leave message before closing connections
    if (currentUser) {
      const leaveMessage: RealTimeMessage = {
        type: 'user_leave',
        userId: currentUser.id,
        userName: currentUser.name,
        timestamp: new Date(),
        data: {},
      };
      broadcastMessage(leaveMessage);
    }

    // Close all peer connections
    peersRef.current.forEach((peer) => {
      if (peer.dataChannel) {
        peer.dataChannel.close();
      }
      peer.connection.close();
    });
    peersRef.current.clear();

    // Remove user from room data
    if (roomId && currentUser) {
      getRoomData(roomId).then(roomData => {
        if (roomData) {
          const updatedUsers = roomData.users.filter(u => u.id !== currentUser.id);
          updateUserList(roomId, updatedUsers);
        }
      }).catch(error => {
        console.error('Failed to update room data on leave:', error);
      });
    }

    // Stop signaling interval
    if (signalingIntervalRef.current) {
      clearInterval(signalingIntervalRef.current);
      signalingIntervalRef.current = null;
    }

    // Reset state
    setIsConnected(false);
    setIsHost(false);
    setRoomId(null);
    setCurrentUser(null);
    setConnectedUsers([]);
    setConnectionStatus('disconnected');
    setError(null);

    // Clear localStorage
    localStorage.removeItem('realtime-room');
  }, [currentUser, roomId, broadcastMessage, getRoomData, updateUserList]);

  // Close room (host only)
  const closeRoom = useCallback(() => {
    if (!isHost || !roomId) return;

    try {
      // Mark room as inactive
      getRoomData(roomId).then(roomData => {
        if (roomData) {
          roomData.isActive = false;
          setRoomData(roomId, roomData);
        }
      }).catch(error => {
        console.error('Failed to close room:', error);
      });

      // Send room closed message to all users
      const closeMessage: RealTimeMessage = {
        type: 'room_closed',
        userId: currentUser?.id || '',
        userName: currentUser?.name || '',
        timestamp: new Date(),
        data: { reason: 'Host closed the room' },
      };
      broadcastMessage(closeMessage);

      // Leave the room
      leaveRoom();
    } catch (error) {
      console.error('Failed to close room:', error);
    }
  }, [isHost, roomId, getRoomData, setRoomData, currentUser, broadcastMessage, leaveRoom]);

  // Send a message to all connected peers
  const sendMessage = useCallback((type: RealTimeMessage['type'], data: unknown) => {
    if (!currentUser) return;

    const message: RealTimeMessage = {
      type,
      userId: currentUser.id,
      userName: currentUser.name,
      timestamp: new Date(),
      data,
    };

    broadcastMessage(message);
  }, [currentUser, broadcastMessage]);

  // Heartbeat to keep user presence updated
  useEffect(() => {
    if (!isConnected || !roomId || !currentUser) return;

    const heartbeatInterval = setInterval(async () => {
      try {
        const roomData = await getRoomData(roomId);
        if (roomData) {
          const updatedUsers = roomData.users.map(u =>
            u.id === currentUser.id ? { ...u, lastSeen: new Date() } : u
          );
          await updateUserList(roomId, updatedUsers);
        }
      } catch (error) {
        console.error('Failed to update heartbeat:', error);
      }
    }, 10000); // Update every 10 seconds

    return () => clearInterval(heartbeatInterval);
  }, [isConnected, roomId, currentUser, getRoomData, updateUserList]);

  // Cross-device message polling using localStorage
  useEffect(() => {
    if (!isConnected || !roomId || !currentUser) return;

    const pollInterval = setInterval(async () => {
      try {
        // Check for new messages from other devices
        const newMessages = await backendService.getMessages(roomId, lastBackendCheckRef.current);

        newMessages.forEach(message => {
          // Only process messages from other users
          if (message.userId !== currentUser.id) {
            console.log('Processing cross-device message:', message.type, 'from:', message.userName);
            messageHandlersRef.current.forEach(handler => handler({
              type: message.type as any,
              userId: message.userId,
              userName: message.userName,
              timestamp: new Date(message.createdAt),
              data: message.data
            }));
          }
        });

        lastBackendCheckRef.current = Date.now();
      } catch (error) {
        console.error('Failed to poll for cross-device updates:', error);
      }
    }, 2000); // Poll every 2 seconds for cross-device updates

    return () => clearInterval(pollInterval);
  }, [isConnected, roomId, currentUser]);

  // Initialize from localStorage on mount
  useEffect(() => {
    if (isInitializedRef.current) return;
    isInitializedRef.current = true;

    const saved = localStorage.getItem('realtime-room');
    if (saved) {
      try {
        const { roomId: savedRoomId, user, isHost: savedIsHost } = JSON.parse(saved);

        // Check if room still exists
        getRoomData(savedRoomId).then(roomData => {
          if (roomData) {
            setRoomId(savedRoomId);
            setCurrentUser(user);
            setIsHost(savedIsHost);
            setIsConnected(true);
            setConnectionStatus('connected');

            // Start signaling
            signalingIntervalRef.current = setInterval(processSignaling, 1000);

            // Update user presence
            const updatedUsers = roomData.users.map(u =>
              u.id === user.id ? { ...u, lastSeen: new Date() } : u
            );
            updateUserList(savedRoomId, updatedUsers);
            setConnectedUsers(updatedUsers);
          } else {
            // Room no longer exists, clear saved data
            localStorage.removeItem('realtime-room');
          }
        }).catch(error => {
          console.error('Failed to restore room:', error);
          localStorage.removeItem('realtime-room');
        });
      } catch (error) {
        console.error('Failed to restore room from localStorage:', error);
        localStorage.removeItem('realtime-room');
      }
    }
  }, [getRoomData, processSignaling, updateUserList]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Stop signaling interval
      if (signalingIntervalRef.current) {
        clearInterval(signalingIntervalRef.current);
      }

      // Close all peer connections
      const peers = peersRef.current;
      peers.forEach((peer) => {
        if (peer.dataChannel) {
          peer.dataChannel.close();
        }
        peer.connection.close();
      });
    };
  }, []);

  return {
    // State
    isConnected,
    isHost,
    roomId,
    currentUser,
    connectedUsers,
    connectionStatus,
    error,

    // Actions
    createRoom,
    joinRoom,
    leaveRoom,
    closeRoom,
    sendMessage,
    addMessageHandler,
    getAvailableRooms,
  };
}
