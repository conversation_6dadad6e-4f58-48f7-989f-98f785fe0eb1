(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{176:(e,t,r)=>{"use strict";r.d(t,{default:()=>S});var a=r(5155),s=r(2115);let o="boss-notification-settings",n={enabled:!0,warningMinutes:[30,10,5],sound:!0,desktop:!0};function i(){let[e,t]=(0,s.useState)(n),[r,a]=(0,s.useState)("default"),[i,l]=(0,s.useState)(!1),[d,c]=(0,s.useState)(!1),[m,p]=(0,s.useState)(null);(0,s.useEffect)(()=>{l(!0)},[]),(0,s.useEffect)(()=>{if(!i)return;let e=localStorage.getItem(o);if(e)try{let r=JSON.parse(e);t({...n,...r})}catch(e){console.error("Failed to parse notification settings:",e)}"Notification"in window&&a(Notification.permission)},[i]),(0,s.useEffect)(()=>{i&&localStorage.setItem(o,JSON.stringify(e))},[e,i]),(0,s.useEffect)(()=>()=>{m&&clearInterval(m)},[m]);let x=(0,s.useCallback)(async()=>{if(!("Notification"in window))return console.warn("This browser does not support notifications"),!1;if("granted"===Notification.permission)return!0;if("denied"===Notification.permission)return!1;let e=await Notification.requestPermission();return a(e),"granted"===e},[]),y=(0,s.useCallback)((t,r)=>{if(e.enabled&&e.desktop&&"granted"===Notification.permission){let e=new Notification(t,{icon:"/favicon.ico",badge:"/favicon.ico",...r});return setTimeout(()=>{e.close()},5e3),e}},[e]),h=(0,s.useCallback)(()=>{if(!e.enabled||!e.sound)return;m&&(clearInterval(m),p(null)),c(!0);let t=()=>{try{let e=new(window.AudioContext||window.webkitAudioContext),t=e.createOscillator(),r=e.createGain();t.connect(r),r.connect(e.destination),t.frequency.value=1e3,t.type="square",r.gain.setValueAtTime(.7,e.currentTime),r.gain.exponentialRampToValueAtTime(.01,e.currentTime+.8),t.start(e.currentTime),t.stop(e.currentTime+.8)}catch(e){console.warn("Could not play notification sound:",e)}};t();let r=setInterval(t,2e3);p(r),setTimeout(()=>{r&&(clearInterval(r),p(null),c(!1))},3e4)},[e,m]);return{settings:e,permission:r,requestPermission:x,showNotification:y,playNotificationSound:h,stopNotificationSound:(0,s.useCallback)(()=>{m&&(clearInterval(m),p(null)),c(!1)},[m]),isPlaying:d,updateSettings:(0,s.useCallback)(e=>{t(t=>({...t,...e}))},[])}}let l=["#3B82F6","#EF4444","#10B981","#F59E0B","#8B5CF6","#EC4899","#06B6D4","#84CC16"];function d(){let[e,t]=(0,s.useState)(!1),[r,a]=(0,s.useState)(!1),[o,n]=(0,s.useState)(null),[i,d]=(0,s.useState)(null),[c,m]=(0,s.useState)([]),[p,x]=(0,s.useState)("disconnected"),[y,h]=(0,s.useState)(null),g=(0,s.useRef)(new Map),u=(0,s.useRef)([]),b=(0,s.useRef)(!1),f=(0,s.useCallback)(()=>Math.random().toString(36).substring(2,8).toUpperCase(),[]),w=(0,s.useCallback)(e=>({id:Math.random().toString(36).substring(2,15),name:e,color:l[Math.floor(Math.random()*l.length)],lastSeen:new Date}),[]),v=(0,s.useCallback)(e=>{let t=JSON.stringify(e);g.current.forEach(e=>{if(e.dataChannel&&"open"===e.dataChannel.readyState)try{e.dataChannel.send(t)}catch(e){console.error("Failed to send message to peer:",e)}})},[]),k=(0,s.useCallback)(e=>(u.current.push(e),()=>{u.current=u.current.filter(t=>t!==e)}),[]),j=(0,s.useCallback)(async e=>{try{x("connecting"),h(null);let r=f(),s=w(e);return n(r),d(s),a(!0),m([s]),t(!0),x("connected"),localStorage.setItem("realtime-room",JSON.stringify({roomId:r,user:s,isHost:!0})),r}catch(e){throw h("Failed to create room"),x("disconnected"),e}},[f,w]),N=(0,s.useCallback)(async(e,r)=>{try{x("connecting"),h(null);let s=w(r);n(e),d(s),a(!1),localStorage.setItem("realtime-room",JSON.stringify({roomId:e,user:s,isHost:!1})),m([s]),t(!0),x("connected")}catch(e){throw h("Failed to join room"),x("disconnected"),e}},[w]),R=(0,s.useCallback)(()=>{g.current.forEach(e=>{e.dataChannel&&e.dataChannel.close(),e.connection.close()}),g.current.clear(),i&&v({type:"user_leave",userId:i.id,userName:i.name,timestamp:new Date,data:{}}),t(!1),a(!1),n(null),d(null),m([]),x("disconnected"),h(null),localStorage.removeItem("realtime-room")},[i,v]),S=(0,s.useCallback)((e,t)=>{i&&v({type:e,userId:i.id,userName:i.name,timestamp:new Date,data:t})},[i,v]);return(0,s.useEffect)(()=>{if(b.current)return;b.current=!0;let e=localStorage.getItem("realtime-room");if(e)try{let{roomId:r,user:s,isHost:o}=JSON.parse(e);n(r),d(s),a(o),m([s]),t(!0),x("connected")}catch(e){console.error("Failed to restore room from localStorage:",e),localStorage.removeItem("realtime-room")}},[]),(0,s.useEffect)(()=>()=>{g.current.forEach(e=>{e.dataChannel&&e.dataChannel.close(),e.connection.close()})},[]),{isConnected:e,isHost:r,roomId:o,currentUser:i,connectedUsers:c,connectionStatus:p,error:y,createRoom:j,joinRoom:N,leaveRoom:R,sendMessage:S,addMessageHandler:k}}let c="boss-timer-state",m=[{id:"chertuba-barracks",location:"Chertuba's Barracks",name:"Chertuba",level:40,respawnTime:6,hp:85e4,minPlayers:3,maxPlayers:8,coordinates:{x:120,y:85},drops:[{name:"Chertuba's Soul Necklace",type:"accessory",rarity:"rare",dropRate:"Low"},{name:"Tsurugi",type:"weapon",rarity:"rare",dropRate:"Low"},{name:"Caliburs Dual Blades",type:"weapon",rarity:"rare",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"Medium"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"Medium"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A powerful orc commander guarding the barracks entrance.",strategy:"Focus on crowd control and maintain distance from his charge attack.",isActive:!1},{id:"chertuba-barracks-awakened",location:"Chertuba's Barracks",name:"Awakened Chertuba",level:55,respawnTime:6,hp:12e5,minPlayers:5,maxPlayers:8,coordinates:{x:120,y:85},drops:[{name:"Guardian's Sword",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Crystal Gaiters",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Apella's Necklace",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Chertuba Tsurugi",type:"weapon",rarity:"rare",dropRate:"Medium"},{name:"Caliburs Dual Blades",type:"weapon",rarity:"rare",dropRate:"Medium"},{name:"Body Slasher",type:"weapon",rarity:"rare",dropRate:"Medium"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"}],description:"The awakened form of Chertuba with enhanced abilities.",strategy:"Requires coordinated team play and strong healing support.",isActive:!1},{id:"southern-wasteland",location:"Southern Wasteland",name:"Basila",level:40,respawnTime:4,hp:65e4,minPlayers:2,maxPlayers:6,coordinates:{x:200,y:150},drops:[{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"Medium"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"Medium"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A fierce beast roaming the wasteland.",strategy:"Kite around rocks to avoid its pounce attack.",isActive:!1},{id:"ruins-of-despair",location:"Ruins of Despair",name:"Kelsus",level:40,respawnTime:10,hp:75e4,minPlayers:3,maxPlayers:7,coordinates:{x:180,y:220},drops:[{name:"Cursed Weapon Enchant Scroll",type:"scroll",rarity:"rare",dropRate:"Medium"},{name:"Cursed Armor Enchant Scroll",type:"scroll",rarity:"rare",dropRate:"Medium"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"An ancient guardian of the ruined temple.",strategy:"Interrupt his channeling abilities to prevent massive damage.",isActive:!1},{id:"ant-nest-b2",location:"Ant Nest (B2)",name:"Savan",level:45,respawnTime:12,hp:11e5,minPlayers:5,maxPlayers:8,coordinates:{x:250,y:200},drops:[{name:"Savan's Robe",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Necklace of Immortality",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Desperion's Staff",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Cursed Weapon Enchant Scroll",type:"scroll",rarity:"rare",dropRate:"Medium"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Epic Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A powerful ant queen deep in the nest.",strategy:"Clear adds first, then focus on the queen.",isActive:!1},{id:"ant-nest-b3",location:"Ant Nest (B3)",name:"Queen Ant",level:50,respawnTime:6,hp:2e6,minPlayers:6,maxPlayers:12,coordinates:{x:75,y:200},drops:[{name:"Queen Ant's Wings",type:"material",rarity:"legendary",dropRate:"Very Low"},{name:"Majestic Robe",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Majestic Gloves",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Majestic Boots",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Majestic Keshanberk",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Ring of Blessing",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Accessory Enchant Scroll",type:"scroll",rarity:"rare",dropRate:"Medium"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"}],description:"The massive queen of the ant colony with devastating area attacks.",strategy:"Requires raid coordination. Focus on adds management and positioning.",isActive:!1},{id:"bloodstained-swampland",location:"Bloodstained Swampland",name:"Tromba",level:60,respawnTime:7,hp:15e5,minPlayers:4,maxPlayers:8,coordinates:{x:95,y:180},drops:[{name:"Damascus Dual Blades",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Demon's Orb",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Blue Wolf Helmet",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A massive swamp creature with toxic abilities.",strategy:"Bring poison resistance and focus on mobility.",isActive:!1},{id:"bee-hive",location:"Bee Hive",name:"Felis",level:40,respawnTime:3,hp:5e5,minPlayers:2,maxPlayers:5,coordinates:{x:160,y:90},drops:[{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"Medium"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"Medium"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Archery Manual (Ultimate Evasion)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Priest's Records (Concentration)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A swift feline predator with high mobility.",strategy:"Quick fight, but watch for his leap attacks.",isActive:!1},{id:"rebel-territory",location:"Rebel Territory",name:"Talakin",level:40,respawnTime:10,hp:8e5,minPlayers:3,maxPlayers:7,coordinates:{x:140,y:170},drops:[{name:"Full Plate Armor",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Full Plate Gauntlets",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Sword of Nightmares",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Crystal Dagger",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Crystallized Ice Bow",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Soul Separator",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"}],description:"Leader of the rebel forces with tactical combat skills.",strategy:"Disrupt his formations and focus fire to prevent reinforcements.",isActive:!1},{id:"dion-plains",location:"Dion Plains",name:"Enkura",level:40,respawnTime:6,hp:6e5,minPlayers:2,maxPlayers:6,coordinates:{x:220,y:130},drops:[{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"Medium"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"Medium"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Battle Tome (Defense Aura)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Art of Dual Blades (Sonic Blaster)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A territorial beast protecting the plains.",strategy:"Straightforward fight, maintain distance from charge attacks.",isActive:!1},{id:"dion-hills",location:"Dion Hills",name:"Pan Dra'eed",level:40,respawnTime:12,hp:9e5,minPlayers:4,maxPlayers:8,coordinates:{x:240,y:110},drops:[{name:"Pan Dra'eed's Necklace",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Drake Leather Boots",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Archery Manual (Energize)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Textbook of Magic (Cancellation)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"An ancient mage with powerful elemental magic.",strategy:"Interrupt his spell casting and use magic resistance.",isActive:!1},{id:"cruma-marshlands",location:"Cruma Marshlands",name:"Mutated Cruma",level:50,respawnTime:8,hp:12e5,minPlayers:4,maxPlayers:8,coordinates:{x:300,y:200},drops:[{name:"Cruma's Horn",type:"material",rarity:"epic",dropRate:"Low"},{name:"Cruma's Shell",type:"material",rarity:"epic",dropRate:"Low"},{name:"Ring of Passion",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Damascus Dual Blades",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Epic Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A mutated creature with enhanced abilities.",strategy:"Focus on positioning and avoid its toxic attacks.",isActive:!1},{id:"morgue",location:"Morgue",name:"Tempest",level:45,respawnTime:6,hp:95e4,minPlayers:3,maxPlayers:7,coordinates:{x:110,y:60},drops:[{name:"Demon's Orb",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Devil's Pact",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Vilesoil Valefar Necklace",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Touch of Life",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"An undead warrior with lightning-fast attacks.",strategy:"Use holy magic and maintain high mobility.",isActive:!1},{id:"cruma-tower-b4",location:"Cruma Tower (B4)",name:"Contaminated Cruma",level:45,respawnTime:8,hp:1e6,minPlayers:4,maxPlayers:8,coordinates:{x:320,y:180},drops:[{name:"Spear Training (Absolute Spear)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Cloak of Authority",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Tiat's Belt",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Resist",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Greatsword Techniques (Reflect Sun)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Archery Manual (Elemental Spike)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"}],description:"A contaminated creature with enhanced toxic abilities.",strategy:"Requires strong poison resistance and coordinated attacks.",isActive:!1},{id:"cruma-tower-b6",location:"Cruma Tower (B6)",name:"Katan",level:55,respawnTime:10,hp:14e5,minPlayers:5,maxPlayers:8,coordinates:{x:340,y:160},drops:[{name:"Feather Eye Buster",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Art of Dual Blades (Breaking Armor)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Boots of Moonsouls Cloak",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Baium's Necklace",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Inheritor's Book (Increase Dual Blades)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Sonic Mastery",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Crossbow Guidebook (Chain Strike)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Blessing",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Blue Wolf Gaiters",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Inferno Ring",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Belt of Blessing",type:"accessory",rarity:"epic",dropRate:"Low"}],description:"A powerful guardian of the tower depths.",strategy:"High-level coordination required, focus on interrupting abilities.",isActive:!1},{id:"cruma-tower-b7",location:"Cruma Tower (B7)",name:"Core Susceptor",level:60,respawnTime:10,hp:16e5,minPlayers:6,maxPlayers:8,coordinates:{x:360,y:140},drops:[{name:"Sarunga",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Lord's Authority",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Susceptor's Heart",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Core Ring",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Dark Crystal Gloves",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Dark Crystal Boots",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Dark Crystal Helmet",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"}],description:"The core guardian of Cruma Tower with immense power.",strategy:"Requires maximum coordination and high-level equipment.",isActive:!1},{id:"delu-dwellings",location:"Delu Dwellings",name:"Sarka",level:45,respawnTime:10,hp:85e4,minPlayers:3,maxPlayers:7,coordinates:{x:190,y:250},drops:[{name:"Full Plate Gaiters",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Full Plate Boots",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A tribal shaman with nature-based magic.",strategy:"Dispel his buffs and avoid standing in nature traps.",isActive:!1},{id:"floran-fields",location:"Floran Fields",name:"Timitris",level:40,respawnTime:8,hp:7e5,minPlayers:3,maxPlayers:6,coordinates:{x:260,y:140},drops:[{name:"Blue Wolf Breastplate",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Blue Wolf Gloves",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Battle Tome (Detect Weakness)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Art of Dual Blades (Detect Weakness)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A plant-like creature that controls the field vegetation.",strategy:"Use fire magic and clear the area of plant minions first.",isActive:!1},{id:"giants-vestige",location:"Giants' Vestige",name:"Stonegeist",level:45,respawnTime:7,hp:1e6,minPlayers:4,maxPlayers:8,coordinates:{x:300,y:80},drops:[{name:"Demon's Dagger",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Stakato Queen's Staff",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Blood Gaiters",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Epic Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Assassination Bible (Venom)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Archery Manual (Rapid Shot)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Assassination Bible (Shadow Blade)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A massive stone golem left by ancient giants.",strategy:"Focus on weak points and use earth-shattering abilities.",isActive:!1},{id:"tanor-canyon",location:"Tanor Canyon",name:"Gahareth",level:70,respawnTime:9,hp:18e5,minPlayers:6,maxPlayers:8,coordinates:{x:400,y:120},drops:[{name:"Dark Legion's Edge",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Majestic Ring",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Bracelet of Blessing",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Belt of Blessing",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Accessory Enchant Scroll",type:"scroll",rarity:"rare",dropRate:"Medium"},{name:"Epic Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Mana Seeker",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A powerful demon lord commanding the canyon.",strategy:"Requires high-level coordination and strong defensive abilities.",isActive:!1},{id:"medusa-garden",location:"Medusa Garden",name:"Medusa",level:55,respawnTime:10,hp:13e5,minPlayers:5,maxPlayers:8,coordinates:{x:380,y:100},drops:[{name:"Medusa's Helm",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Medusa's Cloak",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Themo Tongue",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Epic Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Priest's Records (Arcane Shield)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Art of Dual Blades (Triple Slash)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"The legendary Medusa with petrifying gaze.",strategy:"Avoid direct eye contact and use ranged attacks.",isActive:!1},{id:"death-pass",location:"Death Pass",name:"Black Lily",level:65,respawnTime:12,hp:17e5,minPlayers:6,maxPlayers:8,coordinates:{x:420,y:80},drops:[{name:"Cabrio's Hand",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Demon's Boots",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Black Lily's Magic Necklace",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Accessory Enchant Scroll",type:"scroll",rarity:"rare",dropRate:"Medium"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Epic Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Assassination Bible (Judgment)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Textbook of Magic (Restore Casting)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Priest's Records (Judgment)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Textbook of Magic (Critical Magic)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Assassination Bible (Focus Accuracy)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A dark sorceress with deadly magic.",strategy:"High magic resistance required, interrupt her casting.",isActive:!1},{id:"pillagers-outpost",location:"Pillagers' Outpost",name:"Matura",level:50,respawnTime:6,hp:12e5,minPlayers:4,maxPlayers:8,coordinates:{x:450,y:150},drops:[{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Assassination Bible (Deadly Blow)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Art of Dual Blades (Double Slash)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"Leader of the pillager forces.",strategy:"Fast-paced combat, focus on mobility.",isActive:!1},{id:"brekas-stronghold",location:"Breka's Stronghold",name:"Breka",level:50,respawnTime:6,hp:12e5,minPlayers:4,maxPlayers:8,coordinates:{x:470,y:130},drops:[{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Battle Tome (Deflect Arrow)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Art of Dual Blades (Double Slash)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A fortified stronghold commander.",strategy:"Siege-style combat, coordinate attacks on weak points.",isActive:!1},{id:"gorgon-flower-garden",location:"Gorgon Flower Garden",name:"Pan Marrod",level:50,respawnTime:5,hp:11e5,minPlayers:4,maxPlayers:8,coordinates:{x:500,y:180},drops:[{name:"Akai Longbow",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Battle Tome (Ultimate Defense)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A nature guardian with plant-based abilities.",strategy:"Use fire attacks and clear minions quickly.",isActive:!1},{id:"dragon-valley-north",location:"Dragon Valley (North)",name:"Behemoth",level:65,respawnTime:9,hp:16e5,minPlayers:6,maxPlayers:8,coordinates:{x:520,y:200},drops:[{name:"Sword of Miracles",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Gaiters",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Behemoth Leather Belt",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Epic Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Magic Lizard Storm",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Battle Tome (Double Shock)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Archery Manual (Scooter Stance)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A massive dragon-like creature with devastating attacks.",strategy:"Requires maximum coordination and dragon-slaying equipment.",isActive:!1}],p="custom-bosses",x={common:"text-gray-600 bg-gray-100 dark:text-gray-300 dark:bg-gray-700",rare:"text-blue-600 bg-blue-100 dark:text-blue-300 dark:bg-blue-900",epic:"text-purple-600 bg-purple-100 dark:text-purple-300 dark:bg-purple-900",legendary:"text-yellow-600 bg-yellow-100 dark:text-yellow-300 dark:bg-yellow-900"};function y(e){let{drop:t}=e;return(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:t.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 capitalize",children:t.type})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(x[t.rarity]),children:t.rarity}),t.dropRate&&(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:t.dropRate})]})]})}function h(e){var t;let{boss:r,isOpen:s,onClose:o}=e;return s&&r?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75",onClick:o}),(0,a.jsxs)("div",{className:"inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-2xl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:r.name}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:r.location})]}),(0,a.jsx)("button",{onClick:o,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-3",children:"Stats"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Level:"}),(0,a.jsx)("span",{className:"ml-2 font-medium text-gray-900 dark:text-white",children:r.level})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"HP:"}),(0,a.jsx)("span",{className:"ml-2 font-medium text-gray-900 dark:text-white",children:(null==(t=r.hp)?void 0:t.toLocaleString())||"Unknown"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Respawn:"}),(0,a.jsxs)("span",{className:"ml-2 font-medium text-gray-900 dark:text-white",children:[r.respawnTime,"h"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Players:"}),(0,a.jsxs)("span",{className:"ml-2 font-medium text-gray-900 dark:text-white",children:[r.minPlayers,"-",r.maxPlayers]})]})]})]}),r.coordinates&&(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Location"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Coordinates: (",r.coordinates.x,", ",r.coordinates.y,")",r.coordinates.map&&" - ".concat(r.coordinates.map)]})]}),r.description&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Description"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:r.description})]}),r.strategy&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Strategy"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:r.strategy})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Drops"}),(0,a.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:r.drops.map((e,t)=>(0,a.jsx)(y,{drop:e},"".concat(e.name,"-").concat(e.type,"-").concat(t)))})]})]})]})]})}):null}function g(e){let{isOpen:t,onClose:r}=e,{settings:o,permission:n,requestPermission:l,updateSettings:d}=i(),[c,m]=(0,s.useState)("");if(!t)return null;let p=e=>{d({warningMinutes:o.warningMinutes.filter(t=>t!==e)})},x=async()=>{await l()};return(0,a.jsxs)("div",{className:"fixed inset-0 z-[9999] flex items-center justify-center p-4",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:r}),(0,a.jsxs)("div",{className:"relative w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-white shadow-2xl rounded-2xl border-2 border-gray-300 p-6",style:{backgroundColor:"#ffffff",zIndex:1e4},children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6 border-b border-gray-200 pb-4",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:"Notification Settings"}),(0,a.jsx)("button",{onClick:r,className:"p-2 text-gray-500 hover:text-gray-700 transition-colors rounded-full hover:bg-gray-100",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Browser Permissions"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700",children:["Status: ",(0,a.jsx)("span",{className:"font-medium ".concat("granted"===n?"text-green-600":"denied"===n?"text-red-600":"text-yellow-600"),children:"granted"===n?"Allowed":"denied"===n?"Denied":"Not requested"})]})}),"granted"!==n&&(0,a.jsx)("button",{onClick:x,className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors",children:"Request Permission"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 p-4 rounded-lg border",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:"Enable Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Receive notifications for boss respawns"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:o.enabled,onChange:e=>d({enabled:e.target.checked}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-14 h-7 bg-gray-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:bg-blue-600 shadow-lg"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 p-4 rounded-lg border",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:"Sound Alerts"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Play sound when notifications appear"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:o.sound,onChange:e=>d({sound:e.target.checked}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-14 h-7 bg-gray-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:bg-blue-600 shadow-lg"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 p-4 rounded-lg border",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:"Desktop Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Show desktop notification popups"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:o.desktop,onChange:e=>d({desktop:e.target.checked}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-14 h-7 bg-gray-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:bg-blue-600 shadow-lg"})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg border",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Warning Times"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Get notified X minutes before boss respawn"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:o.warningMinutes.map(e=>(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full border border-blue-200",children:[e," min",(0,a.jsx)("button",{onClick:()=>p(e),className:"ml-2 text-blue-600 hover:text-blue-800 font-bold",children:"\xd7"})]},e))}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("input",{type:"number",value:c,onChange:e=>m(e.target.value),placeholder:"Minutes",min:"1",max:"1440",className:"flex-1 px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsx)("button",{onClick:()=>{let e=parseInt(c);e>0&&!o.warningMinutes.includes(e)&&(d({warningMinutes:[...o.warningMinutes,e].sort((e,t)=>t-e)}),m(""))},disabled:!c||0>=parseInt(c),className:"px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white text-sm rounded-md transition-colors font-medium",children:"Add"})]})]})]})]})]})}function u(e){let{isOpen:t,onClose:r,onAddBoss:o}=e,[n,i]=(0,s.useState)({name:"",location:"",level:1,respawnTime:1}),l=(e,t)=>{i(r=>({...r,[e]:t}))},d=()=>{i({name:"",location:"",level:1,respawnTime:1})};return t?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50",children:[(0,a.jsx)("div",{className:"absolute inset-0",onClick:r}),(0,a.jsxs)("div",{className:"relative w-full max-w-4xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-800 shadow-xl rounded-2xl p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Add New Boss"}),(0,a.jsx)("button",{onClick:r,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),!n.name.trim()||!n.location.trim())return void alert("Please fill in the boss name and location.");o({id:"custom-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9)),name:n.name.trim(),location:n.location.trim(),level:n.level,respawnTime:n.respawnTime,hp:1e5,type:"field",difficulty:"medium",minPlayers:1,maxPlayers:8,drops:[],isActive:!1}),d(),r()},className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Boss Name *"}),(0,a.jsx)("input",{type:"text",value:n.name,onChange:e=>l("name",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Enter boss name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Location *"}),(0,a.jsx)("input",{type:"text",value:n.location,onChange:e=>l("location",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Enter location",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Level"}),(0,a.jsx)("input",{type:"number",min:"1",max:"100",value:n.level,onChange:e=>l("level",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Respawn Time (hours)"}),(0,a.jsx)("input",{type:"number",min:"0.5",step:"0.5",value:n.respawnTime,onChange:e=>l("respawnTime",parseFloat(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-600",children:[(0,a.jsx)("button",{type:"button",onClick:r,className:"px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"Cancel"}),(0,a.jsx)("button",{type:"button",onClick:d,className:"px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors",children:"Reset"}),(0,a.jsx)("button",{type:"submit",className:"px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors",children:"Add Boss"})]})]})]})]}):null}function b(e){let{isOpen:t,onClose:r,onUpdateBoss:o,boss:n}=e,[i,l]=(0,s.useState)({name:"",location:"",level:1,respawnTime:1});(0,s.useEffect)(()=>{n&&l({name:n.name,location:n.location,level:n.level,respawnTime:n.respawnTime})},[n]);let d=(e,t)=>{l(r=>({...r,[e]:t}))};return t&&n?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50",children:[(0,a.jsx)("div",{className:"absolute inset-0",onClick:r}),(0,a.jsxs)("div",{className:"relative w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-800 shadow-xl rounded-2xl p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:["Edit Boss: ",n.name]}),(0,a.jsx)("button",{onClick:r,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),!i.name.trim()||!i.location.trim())return void alert("Please fill in the boss name and location.");n&&o({...n,name:i.name.trim(),location:i.location.trim(),level:i.level,respawnTime:i.respawnTime})},className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Boss Name *"}),(0,a.jsx)("input",{type:"text",value:i.name,onChange:e=>d("name",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Enter boss name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Location *"}),(0,a.jsx)("input",{type:"text",value:i.location,onChange:e=>d("location",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Enter location",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Level"}),(0,a.jsx)("input",{type:"number",min:"1",max:"100",value:i.level,onChange:e=>d("level",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Respawn Time (hours)"}),(0,a.jsx)("input",{type:"number",min:"0.5",step:"0.5",value:i.respawnTime,onChange:e=>d("respawnTime",parseFloat(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-600",children:[(0,a.jsx)("button",{type:"button",onClick:r,className:"px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"Cancel"}),(0,a.jsx)("button",{type:"button",onClick:()=>{n&&l({name:n.name,location:n.location,level:n.level,respawnTime:n.respawnTime})},className:"px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors",children:"Reset"}),(0,a.jsx)("button",{type:"submit",className:"px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-md transition-colors",children:"Update Boss"})]})]})]})]}):null}let f=(e,t)=>btoa(btoa(e)+"|"+btoa(t)),w=(e,t)=>{try{let[r,a]=atob(e).split("|"),s=btoa(t);if(a!==s)return null;return atob(r)}catch(e){return null}};function v(){let[e,t]=(0,s.useState)(!1),[r,a]=(0,s.useState)(!1),[o,n]=(0,s.useState)(null),[i,l]=(0,s.useState)(null),d=(0,s.useCallback)((e,t,r)=>{let a={};return Object.entries(t).forEach(e=>{var t,r;let[s,o]=e;a[s]={isActive:o.isActive,lastKilled:null==(t=o.lastKilled)?void 0:t.toISOString(),nextRespawn:null==(r=o.nextRespawn)?void 0:r.toISOString()}}),{customBosses:e,timerStates:a,sharedAt:new Date().toISOString(),sharedBy:r}},[]),c=(0,s.useCallback)(async(e,r,a,s)=>{t(!0),l(null);try{let t=d(e,r,s),o=JSON.stringify({data:t,hasPassword:!!a});a&&(o=f(o,a));let i=btoa(o),l=window.location.origin+window.location.pathname,c="".concat(l,"?share=").concat(i);return n(c),c}catch(t){let e="Failed to create share link";throw l(e),Error(e)}finally{t(!1)}},[d]),m=(0,s.useCallback)((e,t)=>{a(!0),l(null);try{let r,a=atob(e);try{r=JSON.parse(a)}catch(s){if(!t)return l("This shared data requires a password"),null;let e=w(a,t);if(!e)return l("Invalid password"),null;r=JSON.parse(e)}if(r.hasPassword&&!t)return l("This shared data requires a password"),null;let s={};return Object.entries(r.data.timerStates).forEach(e=>{let[t,r]=e;s[t]={isActive:r.isActive,lastKilled:r.lastKilled?new Date(r.lastKilled):void 0,nextRespawn:r.nextRespawn?new Date(r.nextRespawn):void 0}}),{...r.data,timerStates:s}}catch(e){return l("Invalid share code or corrupted data"),null}finally{a(!1)}},[]),p=(0,s.useCallback)(async e=>{try{return await navigator.clipboard.writeText(e),!0}catch(r){let t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.focus(),t.select();try{return document.execCommand("copy"),document.body.removeChild(t),!0}catch(e){return document.body.removeChild(t),!1}}},[]);return{isSharing:e,isImporting:r,shareUrl:o,error:i,createShareLink:c,parseShareData:m,copyToClipboard:p,clearError:(0,s.useCallback)(()=>{l(null)},[]),clearShareUrl:(0,s.useCallback)(()=>{n(null)},[])}}function k(e){let{isOpen:t,onClose:r,customBosses:o,timerStates:n}=e,[i,l]=(0,s.useState)(""),[d,c]=(0,s.useState)(""),[m,p]=(0,s.useState)(!1),[x,y]=(0,s.useState)(!1),{isSharing:h,shareUrl:g,error:u,createShareLink:b,copyToClipboard:f,clearError:w,clearShareUrl:k}=v(),j=async()=>{w();try{let e=m?i:void 0,t=await b(o,n,e,d||void 0);console.log("Share URL created:",t)}catch(e){console.error("Failed to create share link:",e)}},N=async()=>{g&&await f(g)&&(y(!0),setTimeout(()=>y(!1),2e3))},R=()=>{k(),w(),l(""),c(""),p(!1),y(!1),r()};if(!t)return null;let S={activeBosses:Object.values(n).filter(e=>e.isActive).length,totalBosses:o.length+Object.keys(n).length,customBosses:o.length};return(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50",children:[(0,a.jsx)("div",{className:"absolute inset-0",onClick:R}),(0,a.jsxs)("div",{className:"relative w-full max-w-2xl bg-white dark:bg-gray-800 shadow-xl rounded-2xl p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Share Boss Timers"}),(0,a.jsx)("button",{onClick:R,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"What will be shared:"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:S.totalBosses}),(0,a.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Total Bosses"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:S.activeBosses}),(0,a.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Active Timers"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:S.customBosses}),(0,a.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Custom Bosses"})]})]})]}),g?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-green-100 dark:bg-green-900 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-300 px-4 py-3 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,a.jsx)("span",{className:"font-medium",children:"Share link created successfully!"})]}),(0,a.jsxs)("p",{className:"text-sm",children:["Share this link with others to let them import your boss timers and custom bosses.",m&&" They will need the password you set."]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Share Link"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("input",{type:"text",value:g,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm"}),(0,a.jsx)("button",{onClick:N,className:"px-4 py-2 rounded-md transition-colors text-sm ".concat(x?"bg-green-600 text-white":"bg-gray-600 hover:bg-gray-700 text-white"),children:x?"Copied!":"Copy"})]})]}),m&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Password (share this separately)"}),(0,a.jsx)("div",{className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm font-mono",children:i})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>{k(),l(""),p(!1)},className:"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"Create New Link"}),(0,a.jsx)("button",{onClick:R,className:"flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors",children:"Done"})]})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Your Name (Optional)"}),(0,a.jsx)("input",{type:"text",value:d,onChange:e=>c(e.target.value),placeholder:"Enter your name or guild",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"usePassword",checked:m,onChange:e=>p(e.target.checked),className:"mr-2"}),(0,a.jsx)("label",{htmlFor:"usePassword",className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Password protect this share"})]}),m&&(0,a.jsx)("input",{type:"password",value:i,onChange:e=>l(e.target.value),placeholder:"Enter password",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),u&&(0,a.jsx)("div",{className:"bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded",children:u}),(0,a.jsx)("button",{onClick:j,disabled:h||m&&!i.trim(),className:"w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition-colors flex items-center justify-center gap-2",children:h?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{className:"animate-spin w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Creating Share Link..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"})}),"Create Share Link"]})})]})]})]})}function j(e){let{isOpen:t,onClose:r,onImport:o}=e,[n,i]=(0,s.useState)(""),[l,d]=(0,s.useState)(""),[c,m]=(0,s.useState)("merge"),[p,x]=(0,s.useState)(null),{isImporting:y,error:h,parseShareData:g,clearError:u}=v(),b=()=>{i(""),d(""),m("merge"),x(null),u(),r()};if(!t)return null;let f=p?{activeBosses:Object.values(p.timerStates).filter(e=>e.isActive).length,totalBosses:p.customBosses.length+Object.keys(p.timerStates).length,customBosses:p.customBosses.length,sharedBy:p.sharedBy,sharedAt:new Date(p.sharedAt).toLocaleString()}:null;return(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50",children:[(0,a.jsx)("div",{className:"absolute inset-0",onClick:b}),(0,a.jsxs)("div",{className:"relative w-full max-w-2xl bg-white dark:bg-gray-800 shadow-xl rounded-2xl p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Import Boss Timers"}),(0,a.jsx)("button",{onClick:b,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),p?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-green-100 dark:bg-green-900 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-300 px-4 py-3 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,a.jsx)("span",{className:"font-medium",children:"Share data parsed successfully!"})]}),(0,a.jsx)("p",{className:"text-sm",children:"Review the data below and choose how to import it."})]}),f&&(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Share Information:"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-gray-600 dark:text-gray-400",children:[f.sharedBy&&(0,a.jsxs)("div",{children:["Shared by: ",(0,a.jsx)("span",{className:"font-medium",children:f.sharedBy})]}),(0,a.jsxs)("div",{children:["Shared at: ",(0,a.jsx)("span",{className:"font-medium",children:f.sharedAt})]})]})]}),f&&(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"What will be imported:"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:f.totalBosses}),(0,a.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Total Bosses"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:f.activeBosses}),(0,a.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Active Timers"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:f.customBosses}),(0,a.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Custom Bosses"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Import Mode"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",value:"merge",checked:"merge"===c,onChange:e=>m(e.target.value),className:"mr-2"}),(0,a.jsxs)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:[(0,a.jsx)("strong",{children:"Merge:"})," Add new bosses and update existing timers"]})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",value:"replace",checked:"replace"===c,onChange:e=>m(e.target.value),className:"mr-2"}),(0,a.jsxs)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:[(0,a.jsx)("strong",{children:"Replace:"})," Replace all current data with imported data"]})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>x(null),className:"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"Back"}),(0,a.jsx)("button",{onClick:()=>{p&&(o({customBosses:p.customBosses,timerStates:p.timerStates,sharedBy:p.sharedBy,sharedAt:p.sharedAt}),b())},className:"flex-1 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors",children:"Import Data"})]})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Share Link or Code"}),(0,a.jsx)("textarea",{value:n,onChange:e=>i(e.target.value),placeholder:"Paste the share link or code here...",rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Password (if required)"}),(0,a.jsx)("input",{type:"password",value:l,onChange:e=>d(e.target.value),placeholder:"Enter password if the share is protected",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),h&&(0,a.jsx)("div",{className:"bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded",children:h}),(0,a.jsx)("button",{onClick:()=>{if(u(),!n.trim())return;let e=n.trim();e.includes("?share=")&&(e=new URLSearchParams(e.split("?")[1]).get("share")||"");let t=g(e,l||void 0);t&&x(t)},disabled:y||!n.trim(),className:"w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition-colors flex items-center justify-center gap-2",children:y?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{className:"animate-spin w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Parsing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"Preview Import"]})})]})]})]})}function N(e){let{isOpen:t,onClose:r}=e,[o,n]=(0,s.useState)("create"),[i,l]=(0,s.useState)(""),[c,m]=(0,s.useState)(""),[p,x]=(0,s.useState)(!1),{isConnected:y,isHost:h,roomId:g,currentUser:u,connectedUsers:b,connectionStatus:f,error:w,createRoom:v,joinRoom:k,leaveRoom:j}=d(),N=async()=>{if(i.trim())try{let e=await v(i.trim());console.log("Room created:",e)}catch(e){console.error("Failed to create room:",e)}},R=async()=>{if(i.trim()&&c.trim())try{await k(c.trim().toUpperCase(),i.trim())}catch(e){console.error("Failed to join room:",e)}},S=async()=>{if(g)try{await navigator.clipboard.writeText(g),x(!0),setTimeout(()=>x(!1),2e3)}catch(t){let e=document.createElement("textarea");e.value=g,document.body.appendChild(e),e.select();try{document.execCommand("copy"),x(!0),setTimeout(()=>x(!1),2e3)}catch(e){console.error("Failed to copy room code:",e)}document.body.removeChild(e)}},C=()=>{l(""),m(""),n("create"),x(!1),r()};return t?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50",children:[(0,a.jsx)("div",{className:"absolute inset-0",onClick:C}),(0,a.jsxs)("div",{className:"relative w-full max-w-md bg-white dark:bg-gray-800 shadow-xl rounded-2xl p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 dark:text-white",children:y?"Real-Time Room":"Real-Time Collaboration"}),(0,a.jsx)("button",{onClick:C,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors",children:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),y?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,a.jsx)("span",{className:"font-medium text-green-800 dark:text-green-200",children:"Connected to Room"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-green-700 dark:text-green-300",children:"Room Code:"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("code",{className:"px-2 py-1 bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200 rounded font-mono text-sm",children:g}),(0,a.jsx)("button",{onClick:S,className:"px-2 py-1 rounded text-xs transition-colors ".concat(p?"bg-green-600 text-white":"bg-green-200 dark:bg-green-700 text-green-800 dark:text-green-200 hover:bg-green-300 dark:hover:bg-green-600"),children:p?"Copied!":"Copy"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-green-700 dark:text-green-300",children:"Your Role:"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:h?"Host":"Member"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:["Connected Users (",b.length,")"]}),(0,a.jsx)("div",{className:"space-y-2",children:b.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),(0,a.jsxs)("span",{className:"text-sm text-gray-900 dark:text-white",children:[e.name,e.id===(null==u?void 0:u.id)&&" (You)"]})]},e.id))})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>{j(),C()},className:"flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors",children:"Leave Room"}),(0,a.jsx)("button",{onClick:C,className:"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"Close"})]})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1",children:[(0,a.jsx)("button",{onClick:()=>n("create"),className:"flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ".concat("create"===o?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow":"text-gray-600 dark:text-gray-300"),children:"Create Room"}),(0,a.jsx)("button",{onClick:()=>n("join"),className:"flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ".concat("join"===o?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow":"text-gray-600 dark:text-gray-300"),children:"Join Room"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Your Name"}),(0,a.jsx)("input",{type:"text",value:i,onChange:e=>l(e.target.value),placeholder:"Enter your name",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",maxLength:20})]}),"join"===o&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Room Code"}),(0,a.jsx)("input",{type:"text",value:c,onChange:e=>m(e.target.value.toUpperCase()),placeholder:"Enter room code",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono",maxLength:6})]}),w&&(0,a.jsx)("div",{className:"bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-3 py-2 rounded text-sm",children:w}),(0,a.jsx)("button",{onClick:"create"===o?N:R,disabled:"connecting"===f||!i.trim()||"join"===o&&!c.trim(),className:"w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition-colors flex items-center justify-center gap-2",children:"connecting"===f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{className:"animate-spin w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"create"===o?"Creating...":"Joining..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"})}),"create"===o?"Create Room":"Join Room"]})}),(0,a.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)("svg",{className:"w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsxs)("div",{className:"text-sm text-blue-700 dark:text-blue-300",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Real-Time Collaboration"}),(0,a.jsx)("p",{children:"Share boss timers that update live for all connected users. No import/export needed!"})]})]})})]})]})]}):null}function R(e){var t;let{lastAction:r}=e,{isConnected:s,roomId:o,connectedUsers:n,currentUser:i}=d();return s?(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-4 space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Real-Time Room"})]}),(0,a.jsx)("code",{className:"px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded text-xs font-mono",children:o})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Connected:"}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[n.slice(0,5).map(e=>(0,a.jsx)("div",{className:"w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-medium",style:{backgroundColor:e.color},title:e.name,children:e.name.charAt(0).toUpperCase()},e.id)),n.length>5&&(0,a.jsxs)("div",{className:"w-6 h-6 rounded-full bg-gray-400 flex items-center justify-center text-white text-xs font-medium",children:["+",n.length-5]})]}),(0,a.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["(",n.length," user",1!==n.length?"s":"",")"]})]}),r&&r.userId!==(null==i?void 0:i.id)&&(0,a.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-2",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:(null==(t=n.find(e=>e.id===r.userId))?void 0:t.color)||"#6B7280"}}),(0,a.jsxs)("span",{className:"text-xs text-blue-700 dark:text-blue-300",children:[(0,a.jsx)("strong",{children:r.userName})," ",r.action]}),(0,a.jsx)("span",{className:"text-xs text-blue-600 dark:text-blue-400 ml-auto",children:(e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3),r=Math.floor(t/60);if(t<60)return"just now";{if(r<60)return"".concat(r,"m ago");let e=Math.floor(r/60);return"".concat(e,"h ago")}})(r.timestamp)})]})}),(0,a.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-600 pt-2",children:"Timer changes sync automatically with all connected users"})]}):null}function S(){let{timerState:e,startTimer:t,stopTimer:r,resetTimer:o,getTimeRemaining:n,isRespawned:l,stopNotificationSound:x,isPlaying:y,lastAction:f}=function(){let[e,t]=(0,s.useState)({}),[r,a]=(0,s.useState)(new Date),[o,n]=(0,s.useState)(!1),[l,m]=(0,s.useState)(null),{settings:p,showNotification:x,playNotificationSound:y,stopNotificationSound:h,isPlaying:g}=i(),{isConnected:u,currentUser:b,sendMessage:f,addMessageHandler:w}=d();(0,s.useEffect)(()=>{n(!0)},[]),(0,s.useEffect)(()=>{if(!o)return;let e=localStorage.getItem(c);if(e)try{let r=JSON.parse(e),a={};Object.keys(r).forEach(e=>{a[e]={...r[e],lastKilled:r[e].lastKilled?new Date(r[e].lastKilled):void 0,nextRespawn:r[e].nextRespawn?new Date(r[e].nextRespawn):void 0}}),t(a)}catch(e){console.error("Failed to parse saved timer state:",e)}},[o]),(0,s.useEffect)(()=>{o&&localStorage.setItem(c,JSON.stringify(e))},[e,o]),(0,s.useEffect)(()=>{if(u)return w(r=>{switch(r.type){case"timer_start":{let{bossId:e,killTime:a,respawnHours:s}=r.data,o=new Date(a||r.timestamp),n=new Date(o.getTime()+60*s*6e4);t(t=>({...t,[e]:{lastKilled:o,nextRespawn:n,isActive:!0,notificationSent:!1}})),m({userId:r.userId,userName:r.userName,action:"started timer for ".concat(e),timestamp:new Date(r.timestamp)});break}case"timer_stop":{let{bossId:e}=r.data;t(t=>({...t,[e]:{...t[e],isActive:!1}})),m({userId:r.userId,userName:r.userName,action:"stopped timer for ".concat(e),timestamp:new Date(r.timestamp)});break}case"timer_reset":{let{bossId:e}=r.data;t(t=>{let r={...t};return delete r[e],r}),m({userId:r.userId,userName:r.userName,action:"reset timer for ".concat(e),timestamp:new Date(r.timestamp)});break}case"sync_request":b&&f("sync_response",{timerState:e});break;case"sync_response":{let{timerState:e}=r.data;t(t=>{let r={...t};return Object.entries(e).forEach(e=>{let[t,a]=e,s=r[t],o=new Date(a.lastKilled||0),n=new Date((null==s?void 0:s.lastKilled)||0);(!s||o>n)&&(r[t]={...a,lastKilled:a.lastKilled?new Date(a.lastKilled):void 0,nextRespawn:a.nextRespawn?new Date(a.nextRespawn):void 0})}),r})}}})},[u,b,f,w,e]),(0,s.useEffect)(()=>{u&&b&&f("sync_request",{})},[u,b,f]);let v=(0,s.useCallback)(r=>{Object.entries(e).forEach(e=>{let[a,s]=e;if(!s.isActive||!s.nextRespawn)return;let o=s.nextRespawn.getTime()-r.getTime(),n=Math.floor(o/6e4);if(o<=0&&!s.notificationSent){x("Boss Respawned!",{body:"".concat(a," has respawned and is ready to hunt!"),tag:"boss-respawned-".concat(a)}),y(),t(e=>({...e,[a]:{...e[a],notificationSent:!0}}));return}p.warningMinutes.forEach(e=>{n!==e||s.notificationSent||(x("Boss Respawn Warning",{body:"".concat(a," will respawn in ").concat(e," minutes!"),tag:"boss-warning-".concat(a,"-").concat(e)}),y())})})},[e,p.warningMinutes,x,y]);(0,s.useEffect)(()=>{let e=setInterval(()=>{let e=new Date;a(e),p.enabled&&v(e)},1e3);return()=>clearInterval(e)},[p.enabled,v]);let k=(0,s.useCallback)((e,r,a)=>{let s=a||new Date,o=new Date(s.getTime()+60*r*6e4);t(t=>({...t,[e]:{lastKilled:s,nextRespawn:o,isActive:!0,notificationSent:!1}})),u&&f("timer_start",{bossId:e,killTime:s,respawnHours:r})},[u,f]),j=(0,s.useCallback)(e=>{t(t=>({...t,[e]:{...t[e],isActive:!1}})),u&&f("timer_stop",{bossId:e})},[u,f]),N=(0,s.useCallback)(e=>{t(t=>{let r={...t};return delete r[e],r}),u&&f("timer_reset",{bossId:e})},[u,f]),R=(0,s.useCallback)(t=>{let a=e[t];if(!(null==a?void 0:a.isActive)||!a.nextRespawn)return null;let s=r.getTime(),o=a.nextRespawn.getTime()-s;if(o<=0)return"Respawned!";let n=Math.floor(o/36e5),i=Math.floor(o%36e5/6e4),l=Math.floor(o%6e4/1e3);return n>0?"".concat(n,":").concat(i.toString().padStart(2,"0"),":").concat(l.toString().padStart(2,"0")):"".concat(i,":").concat(l.toString().padStart(2,"0"))},[e,r]),S=(0,s.useCallback)(t=>{let a=e[t];return!!(null==a?void 0:a.isActive)&&!!a.nextRespawn&&a.nextRespawn.getTime()<=r.getTime()},[e,r]);return{timerState:e,currentTime:r,lastAction:l,startTimer:k,stopTimer:j,resetTimer:N,getTimeRemaining:R,isRespawned:S,stopNotificationSound:h,isPlaying:g}}(),{allBosses:w,customBosses:v,addBoss:S,updateBoss:C,deleteBoss:L,isCustomBoss:B}=function(){let[e,t]=(0,s.useState)([]),[r,a]=(0,s.useState)(!1);(0,s.useEffect)(()=>{a(!0)},[]),(0,s.useEffect)(()=>{if(!r)return;let e=localStorage.getItem(p);if(e)try{let r=JSON.parse(e);t(r)}catch(e){console.error("Failed to parse saved custom bosses:",e)}},[r]),(0,s.useEffect)(()=>{r&&localStorage.setItem(p,JSON.stringify(e))},[e,r]);let o=(0,s.useCallback)(()=>[...m,...e],[e]),n=(0,s.useCallback)(e=>{t(t=>[...t,e])},[]),i=(0,s.useCallback)((e,r)=>{t(t=>t.map(t=>t.id===e?{...t,...r}:t))},[]),l=(0,s.useCallback)(e=>!!e.startsWith("custom-")&&(t(t=>t.filter(t=>t.id!==e)),!0),[]),d=(0,s.useCallback)(e=>e.startsWith("custom-"),[]),c=(0,s.useCallback)(e=>o().find(t=>t.id===e),[o]);return{allBosses:o(),customBosses:e,addBoss:n,updateBoss:i,deleteBoss:l,isCustomBoss:d,getBossById:c}}(),[A,T]=(0,s.useState)("name"),[E,M]=(0,s.useState)("all"),[D,P]=(0,s.useState)(""),[H,V]=(0,s.useState)(null),[W,I]=(0,s.useState)(""),[O,q]=(0,s.useState)(null),[F,U]=(0,s.useState)(!1),[z,K]=(0,s.useState)(!1),[G,J]=(0,s.useState)(!1),[_,Q]=(0,s.useState)(null),[Y,X]=(0,s.useState)(!1),[Z,$]=(0,s.useState)(!1),[ee,et]=(0,s.useState)(!1),[er,ea]=(0,s.useState)(!1);(0,s.useEffect)(()=>{ea(!0),new URLSearchParams(window.location.search).get("share")&&$(!0)},[]);let es=w.filter(t=>{var r,a;if("active"===E&&!(null==(r=e[t.id])?void 0:r.isActive)||"inactive"===E&&(null==(a=e[t.id])?void 0:a.isActive))return!1;if(D){let e=D.toLowerCase(),r=t.name.toLowerCase().includes(e),a=t.location.toLowerCase().includes(e);if(!r&&!a)return!1}return!0}).sort((t,r)=>{switch(A){case"name":return t.name.localeCompare(r.name);case"level":return t.level-r.level;case"respawnTime":return t.respawnTime-r.respawnTime;case"location":return t.location.localeCompare(r.location);case"timeOfDeath":var a,s,o,n;let i=(null==(s=e[t.id])||null==(a=s.lastKilled)?void 0:a.getTime())||0;return((null==(n=e[r.id])||null==(o=n.lastKilled)?void 0:o.getTime())||0)-i;default:return 0}}),eo=e=>{t(e.id,e.respawnTime)},en=e=>{V(e.id);let t=new Date;I(new Date(t.getTime()-6e4*t.getTimezoneOffset()).toISOString().slice(0,16))},ei=e=>{if(W){let r=new Date(W);t(e.id,e.respawnTime,r),V(null),I("")}},el=()=>{V(null),I("")},ed=e=>{r(e.id)},ec=e=>{o(e.id)},em=e=>{Q(e),J(!0)},ep=e=>{window.confirm('Are you sure you want to delete "'.concat(e.name,'"? This action cannot be undone.'))&&(L(e.id)?o(e.id):alert("Cannot delete default bosses. Only custom bosses can be deleted."))},ex=t=>{let r=e[t.id];if(!(null==r?void 0:r.isActive))return"bg-gray-100 dark:bg-gray-800";if(l(t.id))return"bg-green-100 dark:bg-green-900";if(r.nextRespawn){let e=Math.floor((r.nextRespawn.getTime()-new Date().getTime())/6e4);if(e<=5)return"bg-red-100 dark:bg-red-900";if(e<=30)return"bg-yellow-100 dark:bg-yellow-900"}return"bg-blue-100 dark:bg-blue-900"},ey=t=>{let r=e[t.id];if(!(null==r?void 0:r.isActive))return"Inactive";if(l(t.id))return"Respawned!";if(r.nextRespawn){let e=Math.floor((r.nextRespawn.getTime()-new Date().getTime())/6e4);if(e<=5)return"Soon!";if(e<=30)return"Warning"}return"Active"},eh=t=>{let r=e[t.id];if(!(null==r?void 0:r.lastKilled))return"-";let s=r.lastKilled,o=s.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit",hour12:!0}),n=s.toLocaleDateString([],{month:"short",day:"numeric"}),i=new Date().getTime()-s.getTime(),l=Math.floor(i/36e5),d=Math.floor(i%36e5/6e4),c="";c=l>0?"".concat(l,"h ").concat(d,"m ago"):d>0?"".concat(d,"m ago"):"Just now";let m=new Date;return s.toDateString()===m.toDateString()?(0,a.jsxs)("div",{className:"text-xs",children:[(0,a.jsx)("div",{className:"font-medium",children:o}),(0,a.jsx)("div",{className:"text-gray-500 dark:text-gray-400",children:c})]}):(0,a.jsxs)("div",{className:"text-xs",children:[(0,a.jsxs)("div",{className:"font-medium",children:[n," ",o]}),(0,a.jsx)("div",{className:"text-gray-500 dark:text-gray-400",children:c})]})};return er?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg shadow space-y-4",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Search"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"text",value:D,onChange:e=>P(e.target.value),placeholder:"Search by name or location...",className:"pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white w-64"}),(0,a.jsx)("svg",{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Sort by"}),(0,a.jsxs)("select",{value:A,onChange:e=>T(e.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,a.jsx)("option",{value:"name",children:"Name"}),(0,a.jsx)("option",{value:"level",children:"Level"}),(0,a.jsx)("option",{value:"respawnTime",children:"Respawn Time"}),(0,a.jsx)("option",{value:"location",children:"Location"}),(0,a.jsx)("option",{value:"timeOfDeath",children:"Time of Death"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Status"}),(0,a.jsxs)("select",{value:E,onChange:e=>M(e.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,a.jsx)("option",{value:"all",children:"All"}),(0,a.jsx)("option",{value:"active",children:"Active"}),(0,a.jsx)("option",{value:"inactive",children:"Inactive"})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("button",{onClick:()=>K(!0),className:"px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded-md transition-colors flex items-center gap-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Add Boss"]}),y&&(0,a.jsxs)("button",{onClick:x,className:"px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-md transition-colors flex items-center gap-2 animate-pulse",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 12a9 9 0 11-6.219-8.56"})}),"Stop Sound"]}),(0,a.jsxs)("button",{onClick:()=>X(!0),className:"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white text-sm rounded-md transition-colors flex items-center gap-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"})}),"Share"]}),(0,a.jsxs)("button",{onClick:()=>et(!0),className:"px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white text-sm rounded-md transition-colors flex items-center gap-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})}),"Live Share"]}),(0,a.jsxs)("button",{onClick:()=>$(!0),className:"px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white text-sm rounded-md transition-colors flex items-center gap-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"})}),"Import"]}),(0,a.jsxs)("button",{onClick:()=>U(!0),className:"px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-md transition-colors flex items-center gap-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h8V9H4v2z"})}),"Notifications"]})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Location"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Boss"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Level"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Respawn Time"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Time Remaining"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Time of Death"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-200 dark:divide-gray-600",children:es.map(t=>{var r,s;return(0,a.jsxs)("tr",{className:"".concat(ex(t)," transition-colors hover:bg-opacity-80"),children:[(0,a.jsx)("td",{className:"px-4 py-4 text-sm text-gray-900 dark:text-white",children:t.location}),(0,a.jsx)("td",{className:"px-4 py-4 text-sm",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("button",{onClick:()=>q(t),className:"font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors",children:t.name}),B(t.id)&&(0,a.jsx)("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-200",children:"Custom"})]})}),(0,a.jsxs)("td",{className:"px-4 py-4 text-sm text-gray-900 dark:text-white",children:["Lv. ",t.level]}),(0,a.jsxs)("td",{className:"px-4 py-4 text-sm text-gray-900 dark:text-white",children:[t.respawnTime,"h",t.respawnVariance&&(0,a.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400 ml-1",children:["(\xb1",t.respawnVariance,"m)"]})]}),(0,a.jsx)("td",{className:"px-4 py-4 text-sm",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat((null==(r=e[t.id])?void 0:r.isActive)?l(t.id)?"bg-green-200 text-green-800 dark:bg-green-800 dark:text-green-200":"bg-blue-200 text-blue-800 dark:bg-blue-800 dark:text-blue-200":"bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-gray-200"),children:ey(t)})}),(0,a.jsx)("td",{className:"px-4 py-4 text-sm font-mono text-gray-900 dark:text-white",children:n(t.id)||"-"}),(0,a.jsx)("td",{className:"px-4 py-4 text-sm text-gray-900 dark:text-white",children:eh(t)}),(0,a.jsx)("td",{className:"px-4 py-4 text-sm",children:H===t.id?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("input",{type:"datetime-local",value:W,onChange:e=>I(e.target.value),className:"w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-xs bg-white dark:bg-gray-700 text-gray-900 dark:text-white"}),(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("button",{onClick:()=>ei(t),className:"px-2 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded transition-colors",children:"✓"}),(0,a.jsx)("button",{onClick:el,className:"px-2 py-1 bg-gray-600 hover:bg-gray-700 text-white text-xs rounded transition-colors",children:"✕"})]})]}):(null==(s=e[t.id])?void 0:s.isActive)?(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)("button",{onClick:()=>ed(t),className:"px-3 py-1 bg-yellow-600 hover:bg-yellow-700 text-white text-xs rounded-md transition-colors",children:"Stop"}),(0,a.jsx)("button",{onClick:()=>ec(t),className:"px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded-md transition-colors",children:"Reset"})]}),B(t.id)&&(0,a.jsxs)("div",{className:"flex flex-col space-y-1 pt-1 border-t border-gray-200 dark:border-gray-600",children:[(0,a.jsx)("button",{onClick:()=>em(t),className:"px-3 py-1 bg-orange-600 hover:bg-orange-700 text-white text-xs rounded-md transition-colors",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>ep(t),className:"px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded-md transition-colors",children:"Delete"})]})]}):(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)("button",{onClick:()=>eo(t),className:"px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded-md transition-colors",children:"Start Now"}),(0,a.jsx)("button",{onClick:()=>en(t),className:"px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-md transition-colors",children:"Set Time"})]}),B(t.id)&&(0,a.jsxs)("div",{className:"flex flex-col space-y-1 pt-1 border-t border-gray-200 dark:border-gray-600",children:[(0,a.jsx)("button",{onClick:()=>em(t),className:"px-3 py-1 bg-orange-600 hover:bg-orange-700 text-white text-xs rounded-md transition-colors",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>ep(t),className:"px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded-md transition-colors",children:"Delete"})]})]})})]},t.id)})})]})})}),(0,a.jsx)(R,{lastAction:f}),y&&(0,a.jsxs)("div",{className:"bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded-lg flex items-center justify-between animate-pulse",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M9 12h6m-6 0a3 3 0 106 0m-6 0a3 3 0 116 0"})}),(0,a.jsx)("span",{className:"font-medium",children:"Boss notification sound is playing!"})]}),(0,a.jsx)("button",{onClick:x,className:"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors",children:"Stop Sound"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg shadow text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:w.length}),(0,a.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Total Bosses"})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg shadow text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:Object.values(e).filter(e=>e.isActive).length}),(0,a.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Active Timers"})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg shadow text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:w.filter(e=>l(e.id)).length}),(0,a.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Ready to Hunt"})]})]}),(0,a.jsx)(h,{boss:O,isOpen:!!O,onClose:()=>q(null)}),(0,a.jsx)(u,{isOpen:z,onClose:()=>K(!1),onAddBoss:S}),(0,a.jsx)(b,{isOpen:G,onClose:()=>{J(!1),Q(null)},onUpdateBoss:e=>{C(e.id,e),J(!1),Q(null)},boss:_}),(0,a.jsx)(k,{isOpen:Y,onClose:()=>X(!1),customBosses:v,timerStates:e}),(0,a.jsx)(j,{isOpen:Z,onClose:()=>$(!1),onImport:e=>{e.customBosses.forEach(e=>{S(e)}),Object.entries(e.timerStates).forEach(e=>{let[r,a]=e;a.isActive&&a.lastKilled&&t(r,a.lastKilled)}),alert("Successfully imported ".concat(e.customBosses.length," custom bosses and timer states!").concat(e.sharedBy?" (Shared by: ".concat(e.sharedBy,")"):""))}}),(0,a.jsx)(g,{isOpen:F,onClose:()=>U(!1)}),(0,a.jsx)(N,{isOpen:ee,onClose:()=>et(!1)})]}):(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"flex justify-center items-center py-12",children:(0,a.jsx)("div",{className:"text-gray-600 dark:text-gray-400",children:"Loading boss timer..."})})})}},4684:(e,t,r)=>{Promise.resolve().then(r.bind(r,176))}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(4684)),_N_E=e.O()}]);