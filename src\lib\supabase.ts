// Simple cross-device sharing using localStorage with room codes
// Users share room codes to connect across devices
// This is a fallback that works without external dependencies

export interface RoomData {
  id: string
  users: Array<{
    id: string
    name: string
    color: string
    lastSeen: string
  }>
  hasPassword: boolean
  password?: string
  host: string
  isActive: boolean
  lastActivity: number
  createdAt: string
}

export interface MessageData {
  id: string
  roomId: string
  userId: string
  userName: string
  type: string
  data: any
  timestamp: number
  createdAt: string
}

// Simple backend service that uses localStorage as fallback
class BackendService {
  // For demo purposes, we'll use localStorage with room codes
  // In a real implementation, this would connect to a backend service

  async createRoom(roomData: RoomData): Promise<void> {
    try {
      // Store room data locally
      localStorage.setItem(`l2m-room-${roomData.id}`, JSON.stringify(roomData))

      // Add to room list
      const roomList = this.getRoomListSync()
      roomList[roomData.id] = {
        id: roomData.id,
        host: roomData.users[0]?.name || 'Unknown',
        userCount: roomData.users.length,
        hasPassword: roomData.hasPassword,
        lastActivity: roomData.lastActivity,
        isActive: roomData.isActive
      }
      localStorage.setItem('l2m-room-list', JSON.stringify(roomList))

      console.log('Room created locally:', roomData.id)
    } catch (error) {
      console.error('Failed to create room:', error)
      throw error
    }
  }

  async getRoomData(roomId: string): Promise<RoomData | null> {
    try {
      const data = localStorage.getItem(`l2m-room-${roomId}`)
      return data ? JSON.parse(data) : null
    } catch (error) {
      console.error('Failed to get room data:', error)
      return null
    }
  }

  async updateRoom(roomId: string, roomData: RoomData): Promise<void> {
    try {
      localStorage.setItem(`l2m-room-${roomId}`, JSON.stringify(roomData))

      // Update room list
      const roomList = this.getRoomListSync()
      roomList[roomId] = {
        id: roomId,
        host: roomData.users[0]?.name || 'Unknown',
        userCount: roomData.users.length,
        hasPassword: roomData.hasPassword,
        lastActivity: roomData.lastActivity,
        isActive: roomData.isActive
      }
      localStorage.setItem('l2m-room-list', JSON.stringify(roomList))
    } catch (error) {
      console.error('Failed to update room:', error)
      throw error
    }
  }

  async deleteRoom(roomId: string): Promise<void> {
    try {
      localStorage.removeItem(`l2m-room-${roomId}`)

      const roomList = this.getRoomListSync()
      delete roomList[roomId]
      localStorage.setItem('l2m-room-list', JSON.stringify(roomList))
    } catch (error) {
      console.error('Failed to delete room:', error)
    }
  }

  private getRoomListSync(): Record<string, any> {
    try {
      const data = localStorage.getItem('l2m-room-list')
      return data ? JSON.parse(data) : {}
    } catch {
      return {}
    }
  }

  async getRoomList(): Promise<Record<string, any>> {
    return this.getRoomListSync()
  }

  // Message management
  async sendMessage(message: MessageData): Promise<void> {
    try {
      const messagesKey = `l2m-messages-${message.roomId}`
      const existingData = localStorage.getItem(messagesKey)
      const messages = existingData ? JSON.parse(existingData) : []

      messages.push(message)

      // Keep only last 100 messages
      if (messages.length > 100) {
        messages.splice(0, messages.length - 100)
      }

      localStorage.setItem(messagesKey, JSON.stringify(messages))
    } catch (error) {
      console.error('Failed to send message:', error)
      throw error
    }
  }

  async getMessages(roomId: string, since: number = 0): Promise<MessageData[]> {
    try {
      const messagesKey = `l2m-messages-${roomId}`
      const data = localStorage.getItem(messagesKey)
      const messages = data ? JSON.parse(data) : []

      return messages.filter((msg: MessageData) => msg.timestamp > since)
    } catch (error) {
      console.error('Failed to get messages:', error)
      return []
    }
  }

  // Simple polling simulation
  async pollForUpdates(lastCheck: number): Promise<{ rooms: Record<string, RoomData>, messages: Record<string, MessageData[]>, lastUpdated: number }> {
    // For localStorage implementation, we don't have real polling
    // This is just a placeholder that returns empty updates
    return { rooms: {}, messages: {}, lastUpdated: Date.now() }
  }
}

export const backendService = new BackendService()
